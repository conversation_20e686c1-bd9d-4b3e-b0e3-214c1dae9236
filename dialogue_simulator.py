#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
维特根斯坦哲学对话模拟器
Wittgenstein Philosophical Dialogue Simulator
"""

import random
import time
from typing import List, Dict, Tuple

class WittgensteinDialogue:
    """维特根斯坦对话模拟器"""
    
    def __init__(self):
        self.dialogue_history = []
        self.current_topic = None
        self.student_confusion_level = 0
        self.wittgenstein_mood = "contemplative"  # contemplative, excited, frustrated, patient
        
        # 维特根斯坦的典型表达方式
        self.wittgenstein_expressions = {
            "questioning": [
                "但是告诉我...",
                "你是什么意思当你说...",
                "在什么情况下你会说...",
                "这里我们必须问...",
                "让我们看看..."
            ],
            "rejection": [
                "不，不，这不对！",
                "这完全搞错了方向！",
                "你被语言迷惑了！",
                "这是一个伪问题！"
            ],
            "insight": [
                "啊！现在我们接近了什么！",
                "看！这里有什么？",
                "不要思考，而要观察！",
                "语言在这里休假了！"
            ],
            "self_correction": [
                "等等，让我重新想想...",
                "不，我说错了...",
                "也许我应该这样说...",
                "让我换个方式..."
            ]
        }
        
        # 日常例子库
        self.everyday_examples = {
            "thinking": [
                "建筑工人在工作时说'让我想想这堵墙怎么建'",
                "棋手在下棋时说'我在思考下一步'",
                "学生在考试时说'我在思考这个问题'"
            ],
            "rules": [
                "交通规则：红灯停，绿灯行",
                "游戏规则：象棋中马走日字",
                "语法规则：主语后面跟谓语"
            ],
            "pain": [
                "孩子摔倒后哭泣",
                "病人向医生描述症状",
                "演员在舞台上表演痛苦"
            ],
            "mathematics": [
                "商店里的找零计算",
                "建筑师的测量工作",
                "孩子学习数数"
            ]
        }
    
    def start_dialogue(self, topic: str) -> str:
        """开始对话"""
        self.current_topic = topic
        self.dialogue_history = []
        
        openings = {
            "thinking": "教授，当我们说'我在思考'时，这个'思考'究竟是什么？",
            "rules": "如果每个人对规则的理解都可能不同，我们如何确保遵循的是同一个规则？",
            "pain": "当我说'我疼痛'时，我如何确定你理解的'疼痛'与我体验的是同一种？",
            "mathematics": "数学看起来如此确定，但您说它只是另一种语言游戏——这怎么可能？"
        }
        
        student_opening = openings.get(topic, "教授，我有一个哲学问题...")
        self.dialogue_history.append(("学生", student_opening))
        
        return self._generate_wittgenstein_response(student_opening)
    
    def _generate_wittgenstein_response(self, student_input: str) -> str:
        """生成维特根斯坦的回应"""
        # 分析学生输入，决定回应策略
        response_strategy = self._analyze_student_input(student_input)
        
        if response_strategy == "socratic_question":
            return self._generate_socratic_question()
        elif response_strategy == "everyday_example":
            return self._generate_everyday_example()
        elif response_strategy == "language_critique":
            return self._generate_language_critique()
        elif response_strategy == "philosophical_therapy":
            return self._generate_philosophical_therapy()
        else:
            return self._generate_general_response()
    
    def _analyze_student_input(self, input_text: str) -> str:
        """分析学生输入，决定回应策略"""
        if "什么是" in input_text or "是什么" in input_text:
            return "socratic_question"
        elif "知道" in input_text or "确定" in input_text:
            return "language_critique"
        elif "规则" in input_text or "遵循" in input_text:
            return "everyday_example"
        elif "数学" in input_text or "确定性" in input_text:
            return "philosophical_therapy"
        else:
            return "general"
    
    def _generate_socratic_question(self) -> str:
        """生成苏格拉底式提问"""
        questions = [
            f"（皱眉，在房间里踱步）{random.choice(self.wittgenstein_expressions['questioning'])}",
            "在什么情况下你会使用这个词？",
            "你能给我一个具体的例子吗？",
            "当你说这个词时，你在做什么？"
        ]
        return random.choice(questions)
    
    def _generate_everyday_example(self) -> str:
        """生成日常例子"""
        if self.current_topic in self.everyday_examples:
            example = random.choice(self.everyday_examples[self.current_topic])
            return f"（停下，转向学生）让我们看一个例子：{example}。这里发生了什么？"
        return "让我们看一个日常的例子..."
    
    def _generate_language_critique(self) -> str:
        """生成语言批判"""
        critiques = [
            "（激动地）你被语言的表面形式误导了！",
            "这个词在这里的用法让你困惑了！",
            "我们必须回到语言的实际使用中去！",
            "不要被语法的相似性欺骗！"
        ]
        return random.choice(critiques)
    
    def _generate_philosophical_therapy(self) -> str:
        """生成哲学治疗"""
        therapies = [
            "这个问题本身就是混乱的产物！",
            "我们需要治疗这种哲学疾病！",
            "让我们回到语言的家乡！",
            "这里没有问题需要解决，只有混乱需要消除！"
        ]
        return random.choice(therapies)
    
    def _generate_general_response(self) -> str:
        """生成一般回应"""
        responses = [
            "（沉思片刻）让我想想...",
            "这很有趣，但是...",
            "你提出了一个重要的观察...",
            "我们必须更仔细地看这个..."
        ]
        return random.choice(responses)
    
    def continue_dialogue(self, student_response: str) -> str:
        """继续对话"""
        self.dialogue_history.append(("学生", student_response))
        wittgenstein_response = self._generate_wittgenstein_response(student_response)
        self.dialogue_history.append(("维特根斯坦", wittgenstein_response))
        return wittgenstein_response
    
    def get_dialogue_history(self) -> List[Tuple[str, str]]:
        """获取对话历史"""
        return self.dialogue_history
    
    def print_dialogue(self):
        """打印对话历史"""
        print("\n" + "="*50)
        print("维特根斯坦哲学对话")
        print("="*50)
        
        for speaker, text in self.dialogue_history:
            print(f"\n{speaker}：{text}")
        print("\n" + "="*50)

def main():
    """主函数 - 交互式对话"""
    dialogue = WittgensteinDialogue()
    
    print("欢迎来到维特根斯坦哲学对话模拟器！")
    print("\n可选主题：")
    print("1. thinking - 什么是思考？")
    print("2. rules - 规则与遵循")
    print("3. pain - 疼痛的本质")
    print("4. mathematics - 数学的确定性")
    
    topic_choice = input("\n请选择一个主题 (1-4): ").strip()
    topic_map = {"1": "thinking", "2": "rules", "3": "pain", "4": "mathematics"}
    topic = topic_map.get(topic_choice, "thinking")
    
    print(f"\n开始关于'{topic}'的对话...")
    print("-" * 30)
    
    # 开始对话
    wittgenstein_response = dialogue.start_dialogue(topic)
    print(f"\n维特根斯坦：{wittgenstein_response}")
    
    # 继续对话循环
    while True:
        student_input = input("\n学生：").strip()
        if student_input.lower() in ['quit', 'exit', '退出', '结束']:
            break
        
        if student_input:
            response = dialogue.continue_dialogue(student_input)
            print(f"\n维特根斯坦：{response}")
    
    print("\n对话结束。")
    dialogue.print_dialogue()

if __name__ == "__main__":
    main()
