class WittgensteinDialogue {
    constructor() {
        this.currentTopic = null;
        this.dialogueHistory = [];
        this.studentConfusionLevel = 0;
        this.wittgensteinMood = 'contemplative';
        
        // 维特根斯坦的典型表达方式
        this.expressions = {
            questioning: [
                "但是告诉我...",
                "你是什么意思当你说...",
                "在什么情况下你会说...",
                "这里我们必须问...",
                "让我们看看..."
            ],
            rejection: [
                "不，不，这不对！",
                "这完全搞错了方向！",
                "你被语言迷惑了！",
                "这是一个伪问题！"
            ],
            insight: [
                "啊！现在我们接近了什么！",
                "看！这里有什么？",
                "不要思考，而要观察！",
                "语言在这里休假了！"
            ],
            selfCorrection: [
                "等等，让我重新想想...",
                "不，我说错了...",
                "也许我应该这样说...",
                "让我换个方式..."
            ]
        };
        
        // 日常例子库
        this.everydayExamples = {
            thinking: [
                "建筑工人在工作时说'让我想想这堵墙怎么建'",
                "棋手在下棋时说'我在思考下一步'",
                "学生在考试时说'我在思考这个问题'"
            ],
            rules: [
                "交通规则：红灯停，绿灯行",
                "游戏规则：象棋中马走日字",
                "语法规则：主语后面跟谓语"
            ],
            pain: [
                "孩子摔倒后哭泣",
                "病人向医生描述症状",
                "演员在舞台上表演痛苦"
            ],
            mathematics: [
                "商店里的找零计算",
                "建筑师的测量工作",
                "孩子学习数数"
            ]
        };
        
        // 主题开场白
        this.topicOpenings = {
            thinking: "教授，当我们说'我在思考'时，这个'思考'究竟是什么？",
            rules: "如果每个人对规则的理解都可能不同，我们如何确保遵循的是同一个规则？",
            pain: "当我说'我疼痛'时，我如何确定你理解的'疼痛'与我体验的是同一种？",
            mathematics: "数学看起来如此确定，但您说它只是另一种语言游戏——这怎么可能？"
        };
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // 主题选择
        document.querySelectorAll('.topic-card').forEach(card => {
            card.addEventListener('click', () => {
                document.querySelectorAll('.topic-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                this.currentTopic = card.dataset.topic;
                document.getElementById('startBtn').disabled = false;
            });
        });
        
        // 开始对话按钮
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startDialogue();
        });
        
        // 返回按钮
        document.getElementById('backBtn').addEventListener('click', () => {
            this.returnToTopicSelection();
        });
        
        // 发送按钮
        document.getElementById('sendBtn').addEventListener('click', () => {
            this.sendMessage();
        });
        
        // 回车发送
        document.getElementById('userInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
    }
    
    startDialogue() {
        document.getElementById('topicSelection').style.display = 'none';
        document.getElementById('dialogueArea').classList.add('active');
        
        this.dialogueHistory = [];
        const studentOpening = this.topicOpenings[this.currentTopic];
        
        this.addMessage('student', studentOpening);
        
        // 延迟显示维特根斯坦的回应
        setTimeout(() => {
            const wittgensteinResponse = this.generateWittgensteinResponse(studentOpening);
            this.addMessage('wittgenstein', wittgensteinResponse);
        }, 1000);
    }
    
    returnToTopicSelection() {
        document.getElementById('dialogueArea').classList.remove('active');
        document.getElementById('topicSelection').style.display = 'block';
        document.getElementById('chatContainer').innerHTML = '';
        this.currentTopic = null;
        document.getElementById('startBtn').disabled = true;
        document.querySelectorAll('.topic-card').forEach(c => c.classList.remove('selected'));
    }
    
    sendMessage() {
        const input = document.getElementById('userInput');
        const message = input.value.trim();
        
        if (message) {
            this.addMessage('student', message);
            input.value = '';
            
            // 延迟显示维特根斯坦的回应
            setTimeout(() => {
                const response = this.generateWittgensteinResponse(message);
                this.addMessage('wittgenstein', response);
            }, 1000 + Math.random() * 2000); // 1-3秒的思考时间
        }
    }
    
    addMessage(speaker, text) {
        const chatContainer = document.getElementById('chatContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${speaker}`;
        
        const speakerName = speaker === 'student' ? '学生' : '维特根斯坦';
        messageDiv.innerHTML = `
            <div class="speaker">${speakerName}</div>
            <div class="text">${text}</div>
        `;
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        this.dialogueHistory.push({ speaker, text });
    }
    
    generateWittgensteinResponse(studentInput) {
        const strategy = this.analyzeStudentInput(studentInput);
        
        switch (strategy) {
            case 'socratic_question':
                return this.generateSocraticQuestion(studentInput);
            case 'everyday_example':
                return this.generateEverydayExample();
            case 'language_critique':
                return this.generateLanguageCritique();
            case 'philosophical_therapy':
                return this.generatePhilosophicalTherapy();
            default:
                return this.generateGeneralResponse();
        }
    }
    
    analyzeStudentInput(input) {
        if (input.includes('什么是') || input.includes('是什么')) {
            return 'socratic_question';
        } else if (input.includes('知道') || input.includes('确定')) {
            return 'language_critique';
        } else if (input.includes('规则') || input.includes('遵循')) {
            return 'everyday_example';
        } else if (input.includes('数学') || input.includes('确定性')) {
            return 'philosophical_therapy';
        } else {
            return 'general';
        }
    }
    
    generateSocraticQuestion(studentInput) {
        const questions = [
            `（皱眉，在房间里踱步）${this.getRandomExpression('questioning')}`,
            "在什么情况下你会使用这个词？",
            "你能给我一个具体的例子吗？",
            "当你说这个词时，你在做什么？",
            "告诉我，你是如何学会使用这个词的？"
        ];
        return this.getRandomItem(questions);
    }
    
    generateEverydayExample() {
        if (this.currentTopic in this.everydayExamples) {
            const example = this.getRandomItem(this.everydayExamples[this.currentTopic]);
            return `（停下，转向学生）让我们看一个例子：${example}。这里发生了什么？`;
        }
        return "让我们看一个日常的例子...";
    }
    
    generateLanguageCritique() {
        const critiques = [
            "（激动地）你被语言的表面形式误导了！",
            "这个词在这里的用法让你困惑了！",
            "我们必须回到语言的实际使用中去！",
            "不要被语法的相似性欺骗！",
            "语言在这里让我们走入了歧途！"
        ];
        return this.getRandomItem(critiques);
    }
    
    generatePhilosophicalTherapy() {
        const therapies = [
            "这个问题本身就是混乱的产物！",
            "我们需要治疗这种哲学疾病！",
            "让我们回到语言的家乡！",
            "这里没有问题需要解决，只有混乱需要消除！",
            "我们被一幅图画俘虏了，而我们无法摆脱它！"
        ];
        return this.getRandomItem(therapies);
    }
    
    generateGeneralResponse() {
        const responses = [
            "（沉思片刻）让我想想...",
            "这很有趣，但是...",
            "你提出了一个重要的观察...",
            "我们必须更仔细地看这个...",
            `${this.getRandomExpression('selfCorrection')}`
        ];
        return this.getRandomItem(responses);
    }
    
    getRandomExpression(category) {
        return this.getRandomItem(this.expressions[category]);
    }
    
    getRandomItem(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
}

// 初始化对话系统
document.addEventListener('DOMContentLoaded', () => {
    new WittgensteinDialogue();
});
