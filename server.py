#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器，用于运行维特根斯坦对话系统
Simple HTTP server for running the Wittgenstein dialogue system
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server(port=8000):
    """启动本地服务器"""
    
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查必要文件是否存在
    required_files = ['index.html', 'dialogue.js']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"错误：缺少必要文件: {', '.join(missing_files)}")
        return False
    
    try:
        # 创建服务器
        handler = http.server.SimpleHTTPRequestHandler
        
        # 添加MIME类型支持
        handler.extensions_map.update({
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.html': 'text/html',
        })
        
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"维特根斯坦哲学对话系统已启动")
            print(f"服务器地址: http://localhost:{port}")
            print(f"按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("已在默认浏览器中打开对话系统")
            except Exception as e:
                print(f"无法自动打开浏览器: {e}")
                print(f"请手动访问: http://localhost:{port}")
            
            print("-" * 50)
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return True
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"端口 {port} 已被占用，尝试使用端口 {port + 1}")
            return start_server(port + 1)
        else:
            print(f"启动服务器时出错: {e}")
            return False
    except Exception as e:
        print(f"未知错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("维特根斯坦哲学对话系统 - 本地服务器")
    print("Wittgenstein Philosophical Dialogue System - Local Server")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("错误：需要Python 3.6或更高版本")
        return
    
    # 获取端口号
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("警告：无效的端口号，使用默认端口8000")
            port = 8000
    
    # 启动服务器
    success = start_server(port)
    
    if success:
        print("\n感谢使用维特根斯坦哲学对话系统！")
    else:
        print("\n服务器启动失败")

if __name__ == "__main__":
    main()
