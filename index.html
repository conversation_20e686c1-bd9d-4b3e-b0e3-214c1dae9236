<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维特根斯坦哲学对话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: #ecf0f1;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(44, 62, 80, 0.9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            font-style: italic;
        }

        .topic-selection {
            padding: 30px;
            background: rgba(52, 73, 94, 0.8);
        }

        .topic-selection h2 {
            margin-bottom: 20px;
            color: #e74c3c;
            font-size: 1.5em;
        }

        .topics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .topic-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .topic-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            border-color: #ecf0f1;
        }

        .topic-card.selected {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-color: #ecf0f1;
        }

        .topic-card h3 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .topic-card p {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .dialogue-area {
            padding: 30px;
            display: none;
        }

        .dialogue-area.active {
            display: block;
        }

        .chat-container {
            height: 400px;
            overflow-y: auto;
            background: rgba(44, 62, 80, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(236, 240, 241, 0.2);
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            animation: fadeIn 0.5s ease;
        }

        .message.student {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            margin-left: 50px;
            text-align: right;
        }

        .message.wittgenstein {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            margin-right: 50px;
        }

        .message .speaker {
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 0.9em;
            opacity: 0.8;
        }

        .message .text {
            line-height: 1.6;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .input-area input {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 25px;
            background: rgba(236, 240, 241, 0.1);
            color: #ecf0f1;
            font-size: 1em;
            outline: none;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-area input:focus {
            border-color: #3498db;
            background: rgba(236, 240, 241, 0.15);
        }

        .input-area input::placeholder {
            color: rgba(236, 240, 241, 0.6);
        }

        .input-area button {
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .input-area button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .start-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .start-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .back-btn {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(149, 165, 166, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .topics {
                grid-template-columns: 1fr;
            }
            
            .message.student {
                margin-left: 20px;
            }
            
            .message.wittgenstein {
                margin-right: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>维特根斯坦哲学对话</h1>
            <p>与20世纪最伟大的哲学家进行思辨对话</p>
        </div>

        <div class="topic-selection" id="topicSelection">
            <h2>选择一个哲学主题</h2>
            <div class="topics">
                <div class="topic-card" data-topic="thinking">
                    <h3>什么是思考？</h3>
                    <p>当我们说"我在思考"时，这个"思考"究竟是什么？</p>
                </div>
                <div class="topic-card" data-topic="rules">
                    <h3>规则与遵循</h3>
                    <p>如果每个人对规则的理解都可能不同，我们如何确保遵循的是同一个规则？</p>
                </div>
                <div class="topic-card" data-topic="pain">
                    <h3>疼痛的本质</h3>
                    <p>当我说"我疼痛"时，我如何确定你理解的"疼痛"与我体验的是同一种？</p>
                </div>
                <div class="topic-card" data-topic="mathematics">
                    <h3>数学的确定性</h3>
                    <p>数学看起来如此确定，但您说它只是另一种语言游戏——这怎么可能？</p>
                </div>
            </div>
            <button class="start-btn" id="startBtn" disabled>开始对话</button>
        </div>

        <div class="dialogue-area" id="dialogueArea">
            <button class="back-btn" id="backBtn">← 返回主题选择</button>
            <div class="chat-container" id="chatContainer">
                <!-- 对话内容将在这里显示 -->
            </div>
            <div class="input-area">
                <input type="text" id="userInput" placeholder="输入你的回应..." maxlength="500">
                <button id="sendBtn">发送</button>
            </div>
        </div>
    </div>

    <script src="dialogue.js"></script>
</body>
</html>
